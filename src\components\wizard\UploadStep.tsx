import { useTranslation } from 'react-i18next';
import { CheckCircle, ArrowRight } from 'lucide-react';
import FileUploader from '../FileUploader';
import FilePreview from '../FilePreview';

interface UploadStepProps {
  files: File[];
  onFilesAccepted: (files: File[]) => void;
  onRemoveFile: (index: number) => void;
  onNextStep: () => void;
  canProceed: boolean;
}

export default function UploadStep({
  files,
  onFilesAccepted,
  onRemoveFile,
  onNextStep,
  canProceed
}: UploadStepProps) {
  const { t } = useTranslation();

  return (
    <div className="space-y-4">
      <div className="bg-dark-100 rounded-xl shadow-lg border border-primary-700 p-6">
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold text-white mb-2 tracking-tight">
            {t('wizard.step1')}
          </h2>
          <p className="text-sm text-primary-400 max-w-2xl mx-auto leading-relaxed">
            {t('hero.subtitle')}
          </p>
          {files.length === 0 && (
            <div className="mt-3 text-xs text-primary-400 bg-dark-50 px-3 py-1 rounded-lg inline-block border border-primary-700">
              ✨ Fluxo simplificado - selecione arquivos e converta em 2 etapas!
            </div>
          )}
        </div>

        <div className="space-y-4">
          <FileUploader onFilesAccepted={onFilesAccepted} />

          {files.length > 0 && (
            <div className="animate-fade-in">
              <h3 className="text-sm font-semibold text-white mb-3 text-center">
                Arquivos Selecionados ({files.length})
              </h3>

              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                {files.map((file, index) => (
                  <FilePreview
                    key={`${file.name}-${index}`}
                    file={file}
                    onRemove={() => onRemoveFile(index)}
                  />
                ))}
              </div>

              <div className="mt-4 text-center space-y-3">
                <div className="inline-flex items-center gap-2 text-primary-400 text-sm">
                  <CheckCircle className="w-4 h-4 text-accent-500" />
                  <span>
                    {files.length === 1
                      ? "1 arquivo selecionado"
                      : `${files.length} arquivos selecionados`
                    }
                  </span>
                </div>

                {canProceed && (
                  <div>
                    <button
                      onClick={onNextStep}
                      className="btn btn-primary flex items-center gap-2 mx-auto px-6 py-3 text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-200"
                    >
                      <span>{t('wizard.next')}</span>
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
