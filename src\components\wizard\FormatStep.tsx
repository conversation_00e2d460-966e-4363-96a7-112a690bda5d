import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCir<PERSON>, Clock, Loader2, Download, RefreshCw, ArrowLeft } from 'lucide-react';
import ConversionOptions from '../ConversionOptions';
import ConvertedFilePreview from '../ConvertedFilePreview';

interface FormatStepProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  filesCount: number;
  files: File[];
  convertedFiles: File[];
  isConverting: boolean;
  onConvert: () => void;
  onDownload: (index: number) => void;
  onDownloadAll: () => void;
  onReset: () => void;
  onGoBack: () => void;
  canGoBack: boolean;
}

export default function FormatStep({
  selectedFormat,
  onFormatChange,
  filesCount,
  files,
  convertedFiles,
  isConverting,
  onConvert,
  onDownload,
  onDownloadAll,
  onReset,
  onGoBack,
  canGoBack
}: FormatStepProps) {
  const { t } = useTranslation();
  const [hasStartedConversion, setHasStartedConversion] = useState(false);

  // Track if conversion has started
  useEffect(() => {
    if (isConverting || convertedFiles.length > 0) {
      setHasStartedConversion(true);
    }
  }, [isConverting, convertedFiles.length]);

  // Reset conversion state when files change or reset is called
  useEffect(() => {
    if (files.length === 0) {
      setHasStartedConversion(false);
    }
  }, [files.length]);

  // Handle reset function to also reset local state
  const handleReset = () => {
    setHasStartedConversion(false);
    onReset();
  };

  const isComplete = convertedFiles.length === files.length && !isConverting;

  // If conversion has started, show conversion interface
  if (hasStartedConversion) {
    return (
      <div className="space-y-4">
        <div className="text-center mb-4">
          <h2 className="text-xl font-bold text-white mb-2 tracking-tight">
            {isComplete
              ? `Conversão Concluída!`
              : isConverting
              ? `Convertendo Arquivos...`
              : `Iniciando Conversão...`
            }
          </h2>
          <p className="text-sm text-primary-400 max-w-2xl mx-auto leading-relaxed">
            {isComplete
              ? `${files.length} arquivo${files.length > 1 ? 's' : ''} convertido${files.length > 1 ? 's' : ''} para ${selectedFormat.toUpperCase()}`
              : `Convertendo ${files.length} arquivo${files.length > 1 ? 's' : ''} para ${selectedFormat.toUpperCase()}`
            }
          </p>
        </div>

        {isConverting && (
          <div className="bg-dark-100 rounded-xl shadow-lg border border-primary-700 p-6 text-center">
            <div className="mb-4">
              <Loader2 className="w-12 h-12 text-accent-500 animate-spin mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">
                {t('converter.processing')}
              </h3>
              <p className="text-primary-400 text-sm">
                Convertendo seus arquivos... Por favor, aguarde.
              </p>
            </div>

            <div className="w-full bg-primary-800 rounded-full h-2">
              <div
                className="bg-accent-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(convertedFiles.length / files.length) * 100}%` }}
              />
            </div>
            <p className="text-xs text-primary-400 mt-2">
              {convertedFiles.length} de {files.length} arquivos convertidos
            </p>
          </div>
        )}

        {convertedFiles.length > 0 && !isConverting && (
          <div className="bg-dark-100 rounded-xl shadow-lg border border-primary-700 p-6 animate-fade-in">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">
                Arquivos Convertidos
              </h3>
              <div className="flex gap-2">
                {canGoBack && (
                  <button
                    onClick={onGoBack}
                    className="btn btn-outline flex items-center gap-1 text-xs"
                  >
                    <ArrowLeft className="w-3 h-3" />
                    Voltar
                  </button>
                )}
                {convertedFiles.length > 1 && (
                  <button
                    onClick={onDownloadAll}
                    className="btn btn-outline flex items-center gap-1 text-xs"
                  >
                    <Download className="w-3 h-3" />
                    Baixar Todos
                  </button>
                )}
                <button
                  onClick={handleReset}
                  className="btn btn-secondary flex items-center gap-1 text-xs"
                >
                  <RefreshCw className="w-3 h-3" />
                  Nova Conversão
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
              {convertedFiles.map((file, index) => (
                <ConvertedFilePreview
                  key={`converted-${file.name}-${index}`}
                  originalFile={file}
                  convertedFormat={selectedFormat}
                  onDownload={() => onDownload(index)}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Default format selection interface
  return (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <div className="flex items-center justify-between mb-3">
          {canGoBack && (
            <button
              onClick={onGoBack}
              className="btn btn-outline flex items-center gap-1 text-xs"
            >
              <ArrowLeft className="w-3 h-3" />
              Voltar
            </button>
          )}
          <div className="flex-1">
            <h2 className="text-xl font-bold text-white mb-1 tracking-tight">
              {t('wizard.step2')}
            </h2>
          </div>
          <div className="w-16"></div> {/* Spacer for balance */}
        </div>
        <p className="text-sm text-primary-400 max-w-2xl mx-auto leading-relaxed">
          Escolha o formato para o qual deseja converter seus {filesCount} arquivo{filesCount > 1 ? 's' : ''}
        </p>
      </div>

      <ConversionOptions
        selectedFormat={selectedFormat}
        onFormatChange={onFormatChange}
      />

      <div className="text-center">
        <div className="inline-flex items-center gap-2 text-primary-400 mb-4 text-sm">
          <CheckCircle className="w-4 h-4 text-accent-500" />
          <span>
            Formato selecionado: <span className="font-semibold text-white">{selectedFormat.toUpperCase()}</span>
          </span>
        </div>

        <div>
          <button
            onClick={onConvert}
            disabled={!selectedFormat || files.length === 0}
            className="btn btn-primary px-6 py-3 text-sm font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {t('converter.convertButton')}
          </button>
        </div>
      </div>
    </div>
  );
}
