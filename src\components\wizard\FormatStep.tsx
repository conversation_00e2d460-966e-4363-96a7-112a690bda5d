import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCir<PERSON>, Clock, Loader2, Download, RefreshCw } from 'lucide-react';
import ConversionOptions from '../ConversionOptions';
import ConvertedFilePreview from '../ConvertedFilePreview';

interface FormatStepProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
  filesCount: number;
  files: File[];
  convertedFiles: File[];
  isConverting: boolean;
  onConvert: () => void;
  onDownload: (index: number) => void;
  onDownloadAll: () => void;
  onReset: () => void;
}

export default function FormatStep({
  selectedFormat,
  onFormatChange,
  filesCount,
  files,
  convertedFiles,
  isConverting,
  onConvert,
  onDownload,
  onDownloadAll,
  onReset
}: FormatStepProps) {
  const { t } = useTranslation();
  const [hasStartedConversion, setHasStartedConversion] = useState(false);

  // Track if conversion has started
  useEffect(() => {
    if (isConverting || convertedFiles.length > 0) {
      setHasStartedConversion(true);
    }
  }, [isConverting, convertedFiles.length]);

  // Reset conversion state when files change or reset is called
  useEffect(() => {
    if (files.length === 0) {
      setHasStartedConversion(false);
    }
  }, [files.length]);

  // Handle reset function to also reset local state
  const handleReset = () => {
    setHasStartedConversion(false);
    onReset();
  };

  const isComplete = convertedFiles.length === files.length && !isConverting;

  // If conversion has started, show conversion interface
  if (hasStartedConversion) {
    return (
      <div className="space-y-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-primary-900 mb-4 tracking-tight">
            {isComplete
              ? `Conversão Concluída!`
              : isConverting
              ? `Convertendo Arquivos...`
              : `Iniciando Conversão...`
            }
          </h2>
          <p className="text-lg text-primary-600 max-w-2xl mx-auto leading-relaxed">
            {isComplete
              ? `${files.length} arquivo${files.length > 1 ? 's' : ''} convertido${files.length > 1 ? 's' : ''} para ${selectedFormat.toUpperCase()}`
              : `Convertendo ${files.length} arquivo${files.length > 1 ? 's' : ''} para ${selectedFormat.toUpperCase()}`
            }
          </p>
        </div>

        {isConverting && (
          <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8 text-center">
            <div className="mb-6">
              <Loader2 className="w-16 h-16 text-accent-500 animate-spin mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-primary-900 mb-2">
                {t('converter.processing')}
              </h3>
              <p className="text-primary-600">
                Convertendo seus arquivos... Por favor, aguarde.
              </p>
            </div>

            <div className="w-full bg-primary-100 rounded-full h-2">
              <div
                className="bg-accent-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(convertedFiles.length / files.length) * 100}%` }}
              />
            </div>
            <p className="text-sm text-primary-500 mt-2">
              {convertedFiles.length} de {files.length} arquivos convertidos
            </p>
          </div>
        )}

        {convertedFiles.length > 0 && !isConverting && (
          <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8 animate-fade-in">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-semibold text-primary-900">
                Arquivos Convertidos
              </h3>
              <div className="flex gap-3">
                {convertedFiles.length > 1 && (
                  <button
                    onClick={onDownloadAll}
                    className="btn btn-outline flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Baixar Todos (.zip)
                  </button>
                )}
                <button
                  onClick={handleReset}
                  className="btn btn-secondary flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  Nova Conversão
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {convertedFiles.map((file, index) => (
                <ConvertedFilePreview
                  key={`converted-${file.name}-${index}`}
                  originalFile={file}
                  convertedFormat={selectedFormat}
                  onDownload={() => onDownload(index)}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Default format selection interface
  return (
    <div className="space-y-8">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-primary-900 mb-4 tracking-tight">
          {t('wizard.step2')}
        </h2>
        <p className="text-lg text-primary-600 max-w-2xl mx-auto leading-relaxed">
          Escolha o formato para o qual deseja converter seus {filesCount} arquivo{filesCount > 1 ? 's' : ''}
        </p>
      </div>

      <ConversionOptions
        selectedFormat={selectedFormat}
        onFormatChange={onFormatChange}
      />

      <div className="text-center">
        <div className="inline-flex items-center gap-2 text-primary-600 mb-6">
          <CheckCircle className="w-5 h-5 text-accent-500" />
          <span>
            Formato selecionado: <span className="font-semibold text-primary-900">{selectedFormat.toUpperCase()}</span>
          </span>
        </div>

        <div>
          <button
            onClick={onConvert}
            disabled={!selectedFormat || files.length === 0}
            className="btn btn-primary px-8 py-4 text-base font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {t('converter.convertButton')}
          </button>
        </div>
      </div>
    </div>
  );
}
