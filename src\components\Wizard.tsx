import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Check } from 'lucide-react';
import { cn } from '../utils/cn';

interface WizardStep {
  id: string;
  title: string;
  component: React.ReactNode;
}

interface WizardProps {
  steps: WizardStep[];
  currentStep: number;
  onComplete?: () => void;
}

export default function Wizard({ steps, currentStep, onComplete }: WizardProps) {
  const { t } = useTranslation();

  // Auto-complete when reaching the last step
  useEffect(() => {
    if (currentStep >= steps.length - 1 && onComplete) {
      // Optional: auto-complete logic can be added here if needed
    }
  }, [currentStep, steps.length, onComplete]);

  return (
    <div className="w-full max-w-5xl mx-auto">
      {/* Progress Indicator */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-500",
                  index < currentStep
                    ? "bg-accent-600 border-accent-600 text-white"
                    : index === currentStep
                    ? "bg-accent-500 border-accent-500 text-white animate-pulse"
                    : "bg-dark-100 border-primary-700 text-primary-400"
                )}
              >
                {index < currentStep ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <span className="text-xs font-semibold">{index + 1}</span>
                )}
              </div>

              <div className="ml-2 hidden sm:block">
                <p className={cn(
                  "text-xs font-medium transition-colors duration-500",
                  index <= currentStep ? "text-white" : "text-primary-500"
                )}>
                  {step.title}
                </p>
              </div>

              {index < steps.length - 1 && (
                <div className={cn(
                  "hidden sm:block w-12 h-0.5 mx-3 transition-all duration-500",
                  index < currentStep ? "bg-accent-600" : "bg-primary-700"
                )} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="mb-4">
        <div className="animate-fade-in">
          {steps[currentStep]?.component}
        </div>
      </div>


    </div>
  );
}


