import { useTranslation } from 'react-i18next';

export default function Hero() {
  const { t } = useTranslation();

  return (
    <div className="bg-gradient-to-r from-primary-600 to-secondary-600 text-white py-16 md:py-24">
      <div className="container-custom">
        <div className="max-w-3xl mx-auto text-center animate-fade-in">
          <h1 className="text-3xl md:text-5xl font-bold mb-6">
            {t('hero.title')}
          </h1>
          <p className="text-lg md:text-xl text-white/90 mb-8">
            {t('hero.subtitle')}
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <a 
              href="#converter" 
              className="btn bg-white text-primary-600 hover:bg-gray-100 px-6 py-3 rounded-md font-medium text-base"
            >
              Começar Agora
            </a>
            <a 
              href="#formats" 
              className="btn border-2 border-white bg-transparent hover:bg-white/10 text-white px-6 py-3 rounded-md font-medium text-base"
            >
              Ver Formatos Suportados
            </a>
          </div>
        </div>
      </div>
      
      <div className="absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-white/5 to-transparent"></div>
    </div>
  );
}