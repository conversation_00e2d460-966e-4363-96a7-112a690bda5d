import { useEffect, useState } from 'react';
import { X, Image } from 'lucide-react';

interface FilePreviewProps {
  file: File;
  onRemove: () => void;
}

export default function FilePreview({ file, onRemove }: FilePreviewProps) {
  const [preview, setPreview] = useState<string | null>(null);

  useEffect(() => {
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = () => {
      setPreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    return () => {
      // Revoke object URL when component unmounts to avoid memory leaks
      if (preview) URL.revokeObjectURL(preview);
    };
  }, [file]);

  if (!file || !preview) {
    return (
      <div className="flex items-center justify-center bg-gray-100 rounded-lg h-40 w-full">
        <Image className="w-10 h-10 text-gray-400" />
      </div>
    );
  }

  return (
    <div className="relative group rounded-lg overflow-hidden shadow-sm border border-gray-200 bg-white">
      <img
        src={preview}
        alt={file.name}
        className="w-full h-40 object-contain bg-gray-50 p-2"
      />
      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
        <button
          onClick={onRemove}
          className="p-2 rounded-full bg-white text-gray-700 hover:text-red-500 transition-colors"
          aria-label="Remove file"
        >
          <X className="w-4 h-4" />
        </button>
      </div>
      <div className="p-3">
        <p className="text-sm font-medium text-gray-700 truncate" title={file.name}>
          {file.name}
        </p>
        <p className="text-xs text-gray-500">
          {Math.round(file.size / 1024)} KB
        </p>
      </div>
    </div>
  );
}