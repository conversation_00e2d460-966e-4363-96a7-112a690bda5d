import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDropzone } from 'react-dropzone';
import { Upload, File } from 'lucide-react';
import { cn } from '../utils/cn';

interface FileUploaderProps {
  onFilesAccepted: (files: File[]) => void;
}

export default function FileUploader({ onFilesAccepted }: FileUploaderProps) {
  const { t } = useTranslation();
  const [isDragActive, setIsDragActive] = useState(false);
  
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles?.length) {
      onFilesAccepted(acceptedFiles);
    }
  }, [onFilesAccepted]);

  const { getRootProps, getInputProps, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'image/*': [
        '.png', '.jpg', '.jpeg', '.gif', '.ico', '.webp', '.svg',
        '.bmp', '.tiff', '.tif', '.heic', '.heif', '.raw', '.cr2',
        '.nef', '.arw', '.dng', '.psd', '.ai', '.eps'
      ],
      'application/pdf': ['.pdf']
    },
    maxSize: 50 * 1024 * 1024, // 50MB para suportar arquivos maiores como PSD/RAW
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false)
  });

  return (
    <div
      {...getRootProps()}
      className={cn(
        "drop-area cursor-pointer focus:outline-none",
        isDragActive && "drop-area-active",
        isDragReject && "border-red-500 bg-red-900/20"
      )}
    >
      <input {...getInputProps()} />

      <div className="flex flex-col items-center justify-center text-center">
        <div className="rounded-xl bg-gradient-to-br from-accent-600 to-accent-700 p-3 mb-3 shadow-lg">
          <Upload className="w-6 h-6 text-white" />
        </div>

        <h3 className="text-sm font-semibold text-white mb-1">
          {isDragActive
            ? t('converter.dropzoneActive')
            : t('converter.dragHere')}
        </h3>

        <p className="text-primary-400 text-xs">
          {t('converter.orClick')}
        </p>

        {isDragReject && (
          <p className="mt-3 text-xs text-red-400 bg-red-900/20 px-3 py-1 rounded-lg border border-red-700">
            Arquivo não suportado. Por favor, selecione apenas arquivos de imagem.
          </p>
        )}
      </div>
    </div>
  );
}