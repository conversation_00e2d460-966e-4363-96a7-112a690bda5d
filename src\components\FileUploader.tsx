import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDropzone } from 'react-dropzone';
import { Upload, File } from 'lucide-react';
import { cn } from '../utils/cn';

interface FileUploaderProps {
  onFilesAccepted: (files: File[]) => void;
}

export default function FileUploader({ onFilesAccepted }: FileUploaderProps) {
  const { t } = useTranslation();
  const [isDragActive, setIsDragActive] = useState(false);
  
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles?.length) {
      onFilesAccepted(acceptedFiles);
    }
  }, [onFilesAccepted]);

  const { getRootProps, getInputProps, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'image/*': [
        '.png', '.jpg', '.jpeg', '.gif', '.ico', '.webp', '.svg',
        '.bmp', '.tiff', '.tif', '.heic', '.heif', '.raw', '.cr2',
        '.nef', '.arw', '.dng', '.psd', '.ai', '.eps'
      ],
      'application/pdf': ['.pdf']
    },
    maxSize: 50 * 1024 * 1024, // 50MB para suportar arquivos maiores como PSD/RAW
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false)
  });

  return (
    <div 
      {...getRootProps()} 
      className={cn(
        "drop-area cursor-pointer bg-white focus:outline-none",
        isDragActive && "drop-area-active",
        isDragReject && "border-red-500 bg-red-50"
      )}
    >
      <input {...getInputProps()} />
      
      <div className="flex flex-col items-center justify-center text-center">
        <div className="rounded-2xl bg-gradient-to-br from-accent-100 to-accent-200 p-6 mb-6 shadow-lg">
          <Upload className="w-12 h-12 text-accent-600" />
        </div>

        <h3 className="text-xl font-semibold text-primary-900 mb-2">
          {isDragActive
            ? t('converter.dropzoneActive')
            : t('converter.dragHere')}
        </h3>

        <p className="text-primary-600 mb-4">
          {t('converter.orClick')}
        </p>

        <p className="text-sm text-primary-500 bg-primary-50 px-4 py-2 rounded-xl">
          {t('converter.uploadSubtitle')}
        </p>

        {isDragReject && (
          <p className="mt-4 text-sm text-red-600 bg-red-50 px-4 py-2 rounded-xl">
            Arquivo não suportado. Por favor, selecione apenas arquivos de imagem.
          </p>
        )}
      </div>
    </div>
  );
}