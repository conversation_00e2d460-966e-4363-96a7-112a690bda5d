import jsPDF from 'jspdf';

export async function convertImage(
  originalImage: File,
  targetFormat: string,
  quality: number = 0.9
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        canvas.width = img.width;
        canvas.height = img.height;

        // Handle different background colors for different formats
        if (targetFormat === 'jpg' || targetFormat === 'jpeg' || targetFormat === 'bmp') {
          // Fill with white background for formats that don't support transparency
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        // Draw the image
        ctx.drawImage(img, 0, 0);

        // Handle special formats
        if (targetFormat === 'pdf') {
          // For PDF, we'll create a proper PDF with the image using jsPDF
          convertToPDF(canvas, originalImage.name, originalImage.type).then(resolve).catch(reject);
          return;
        }

        // Get the appropriate MIME type
        const mimeType = getMimeType(targetFormat);

        // Convert to blob
        canvas.toBlob((blob) => {
          URL.revokeObjectURL(url);
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert image'));
          }
        }, mimeType, quality);

      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };

    img.src = url;
  });
}

function getMimeType(format: string): string {
  const mimeTypes: Record<string, string> = {
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'webp': 'image/webp',
    'bmp': 'image/bmp',
    'tiff': 'image/tiff',
    'ico': 'image/x-icon',
    'svg': 'image/svg+xml'
  };

  return mimeTypes[format] || 'image/png';
}

async function convertToPDF(canvas: HTMLCanvasElement, originalName: string, originalType?: string): Promise<Blob> {
  try {
    // Validate canvas
    if (!canvas || canvas.width === 0 || canvas.height === 0) {
      throw new Error('Invalid canvas provided for PDF conversion');
    }

    // Determine the best format for PDF embedding
    // PNG for images with transparency, JPEG for others (better compression)
    const useJPEG = !originalType?.includes('png');
    const imgData = useJPEG
      ? canvas.toDataURL('image/jpeg', 0.95)
      : canvas.toDataURL('image/png');

    // Validate image data
    if (!imgData || imgData === 'data:,') {
      throw new Error('Failed to extract image data from canvas');
    }

    // Calculate dimensions
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const aspectRatio = imgWidth / imgHeight;

    // Determine optimal orientation and page size
    let orientation: 'portrait' | 'landscape';
    let pageWidth: number;
    let pageHeight: number;

    if (aspectRatio > 1.4) {
      // Wide image - use landscape
      orientation = 'landscape';
      pageWidth = 297; // A4 landscape width
      pageHeight = 210; // A4 landscape height
    } else {
      // Tall or square image - use portrait
      orientation = 'portrait';
      pageWidth = 210; // A4 portrait width
      pageHeight = 297; // A4 portrait height
    }

    // Add margins (10mm on each side)
    const margin = 10;
    const availableWidth = pageWidth - (2 * margin);
    const availableHeight = pageHeight - (2 * margin);

    // Calculate scaling to fit image within available space
    const widthScale = availableWidth / imgWidth;
    const heightScale = availableHeight / imgHeight;
    const scale = Math.min(widthScale, heightScale, 1); // Don't upscale

    // Calculate final dimensions
    const finalWidth = imgWidth * scale;
    const finalHeight = imgHeight * scale;

    // Center the image on the page
    const xOffset = (pageWidth - finalWidth) / 2;
    const yOffset = (pageHeight - finalHeight) / 2;

    // Create new jsPDF instance
    const pdf = new jsPDF({
      orientation,
      unit: 'mm',
      format: 'a4',
      compress: true
    });

    // Set PDF metadata
    pdf.setProperties({
      title: `Converted Image - ${originalName}`,
      subject: 'Image converted to PDF by ICOnverter',
      author: 'ICOnverter',
      creator: 'ICOnverter - Image Conversion Tool'
    });

    // Add the image to PDF
    pdf.addImage(
      imgData,
      useJPEG ? 'JPEG' : 'PNG',
      xOffset,
      yOffset,
      finalWidth,
      finalHeight,
      `image_${Date.now()}`, // Unique alias
      'FAST'
    );

    // Convert to blob
    const pdfBlob = pdf.output('blob');

    // Validate the generated PDF blob
    if (!pdfBlob || pdfBlob.size === 0) {
      throw new Error('Generated PDF is empty or invalid');
    }

    // Additional validation - check if it's a valid PDF by checking the header
    const arrayBuffer = await pdfBlob.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    const pdfHeader = String.fromCharCode(...uint8Array.slice(0, 4));

    if (pdfHeader !== '%PDF') {
      throw new Error('Generated file is not a valid PDF');
    }

    return pdfBlob;
  } catch (error) {
    console.error('Error creating PDF:', error);
    throw new Error('Failed to create PDF: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}

// Legacy function for backward compatibility
export async function createPixelatedImage(
  originalImage: File,
  scale: number = 8
): Promise<Blob> {
  return convertImage(originalImage, 'png', 0.9);
}