import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import J<PERSON><PERSON><PERSON> from 'jszip';
import Wizard from './Wizard';
import UploadStep from './wizard/UploadStep';
import FormatStep from './wizard/FormatStep';
import { convertImage } from '../utils/imageProcessing';

export default function Converter() {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  const [selectedFormat, setSelectedFormat] = useState<string>('png');
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [convertedFiles, setConvertedFiles] = useState<File[]>([]);
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [hasSelectedFormat, setHasSelectedFormat] = useState<boolean>(false);
  
  const handleFilesAccepted = useCallback((acceptedFiles: File[]) => {
    setFiles((prevFiles) => [...prevFiles, ...acceptedFiles]);
  }, []);
  
  const handleRemoveFile = useCallback((index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    setConvertedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
  }, []);
  
  const handleConvert = useCallback(async () => {
    if (!files.length) return;

    setIsConverting(true);
    setConvertedFiles([]); // Reset converted files

    try {
      const convertedBlobs = await Promise.all(
        files.map(file => convertImage(file, selectedFormat, 0.9))
      );

      const convertedFiles = convertedBlobs.map((blob, index) => {
        const fileName = files[index].name.split('.')[0];
        const mimeType = selectedFormat === 'pdf' ? 'application/pdf' : `image/${selectedFormat}`;
        return new File([blob], `${fileName}.${selectedFormat}`, {
          type: mimeType
        });
      });

      setConvertedFiles(convertedFiles);
    } catch (error) {
      console.error('Conversion error:', error);
      // TODO: Add error handling UI
    } finally {
      setIsConverting(false);
    }
  }, [files, selectedFormat]);
  
  const handleDownload = useCallback((index: number) => {
    if (!convertedFiles[index]) return;
    
    const file = convertedFiles[index];
    const url = URL.createObjectURL(file);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);

  const handleDownloadAll = useCallback(async () => {
    if (!convertedFiles.length) return;

    const zip = new JSZip();

    // Add all files to the zip
    convertedFiles.forEach((file) => {
      zip.file(file.name, file);
    });

    // Generate the zip file
    const content = await zip.generateAsync({ type: 'blob' });

    // Create download link
    const url = URL.createObjectURL(content);
    const link = document.createElement('a');
    link.href = url;
    link.download = `converted_images.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);

  const handleReset = useCallback(() => {
    setFiles([]);
    setConvertedFiles([]);
    setSelectedFormat('png');
    setIsConverting(false);
    setCurrentStep(0);
    setHasSelectedFormat(false);
  }, []);

  const handleGoBack = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      setConvertedFiles([]);
      setIsConverting(false);
    }
  }, [currentStep]);

  const handleFormatChange = useCallback((format: string) => {
    setSelectedFormat(format);
    setHasSelectedFormat(true);
  }, []);

  // Auto-transition from Step 1 to Step 2 when files are selected
  useEffect(() => {
    if (currentStep === 0 && files.length > 0) {
      const timer = setTimeout(() => {
        setCurrentStep(1);
      }, 1500); // 1.5 second delay

      return () => clearTimeout(timer);
    }
  }, [files.length, currentStep]);

  const canProceedToStep = useCallback((stepIndex: number) => {
    switch (stepIndex) {
      case 0: // Upload step
        return true; // Always can start
      case 1: // Format step & conversion
        return files.length > 0; // Need files to proceed
      default:
        return true;
    }
  }, [files.length]);
  
  const wizardSteps = [
    {
      id: 'upload',
      title: t('wizard.step1'),
      component: (
        <UploadStep
          files={files}
          onFilesAccepted={handleFilesAccepted}
          onRemoveFile={handleRemoveFile}
        />
      )
    },
    {
      id: 'format',
      title: t('wizard.step2'),
      component: (
        <FormatStep
          selectedFormat={selectedFormat}
          onFormatChange={handleFormatChange}
          filesCount={files.length}
          files={files}
          convertedFiles={convertedFiles}
          isConverting={isConverting}
          onConvert={handleConvert}
          onDownload={handleDownload}
          onDownloadAll={handleDownloadAll}
          onReset={handleReset}
          onGoBack={handleGoBack}
          canGoBack={currentStep > 0}
        />
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="text-center mb-4">
        <h1 className="text-2xl font-bold text-white mb-2 tracking-tight">
          {t('hero.title')}
        </h1>
        <p className="text-sm text-primary-400 max-w-2xl mx-auto leading-relaxed">
          {t('hero.subtitle')}
        </p>
      </div>

      <Wizard steps={wizardSteps} currentStep={currentStep} />
    </div>
  );
}