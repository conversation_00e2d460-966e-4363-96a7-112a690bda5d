import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle } from 'lucide-react';
import { cn } from '../utils/cn';

interface FormatOption {
  id: string;
  icon: string;
}

interface ConversionOptionsProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
}

export default function ConversionOptions({ 
  selectedFormat, 
  onFormatChange 
}: ConversionOptionsProps) {
  const { t } = useTranslation();
  
  const formats = useMemo<FormatOption[]>(() => [
    { id: 'ico', icon: '🖼️' },
    { id: 'png', icon: '🎨' },
    { id: 'jpg', icon: '📷' },
    { id: 'gif', icon: '🎞️' },
    { id: 'webp', icon: '🌐' },
    { id: 'svg', icon: '✨' },
    { id: 'bmp', icon: '🖌️' },
    { id: 'tiff', icon: '📄' },
    { id: 'pdf', icon: '📋' },
  ], []);

  return (
    <div className="bg-white rounded-3xl shadow-lg border border-primary-100 p-8">
      <h3 className="text-2xl font-semibold text-primary-900 mb-6 text-center">
        {t('converter.selectFormat')}
      </h3>

      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
        {formats.map((format) => (
          <button
            key={format.id}
            className={cn(
              "relative flex flex-col items-center justify-center p-6 rounded-2xl border-2 transition-all duration-300 hover:scale-105",
              selectedFormat === format.id
                ? "border-accent-500 bg-accent-50 shadow-lg"
                : "border-primary-200 bg-white hover:border-accent-300 hover:bg-accent-50/50 hover:shadow-md"
            )}
            onClick={() => onFormatChange(format.id)}
          >
            <span className="text-3xl mb-3">{format.icon}</span>
            <span className="text-sm font-semibold text-primary-700">
              {t(`formats.${format.id}`)}
            </span>

            {selectedFormat === format.id && (
              <div className="absolute -top-2 -right-2">
                <CheckCircle className="w-6 h-6 text-accent-500 fill-white shadow-lg rounded-full" />
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}