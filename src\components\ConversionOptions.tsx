import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CheckCircle } from 'lucide-react';
import { cn } from '../utils/cn';

interface FormatOption {
  id: string;
  icon: string;
}

interface ConversionOptionsProps {
  selectedFormat: string;
  onFormatChange: (format: string) => void;
}

export default function ConversionOptions({ 
  selectedFormat, 
  onFormatChange 
}: ConversionOptionsProps) {
  const { t } = useTranslation();
  
  const formats = useMemo<FormatOption[]>(() => [
    { id: 'ico', icon: '🖼️' },
    { id: 'png', icon: '🎨' },
    { id: 'jpg', icon: '📷' },
    { id: 'gif', icon: '🎞️' },
    { id: 'webp', icon: '🌐' },
    { id: 'svg', icon: '✨' },
    { id: 'bmp', icon: '🖌️' },
    { id: 'tiff', icon: '📄' },
    { id: 'pdf', icon: '📋' },
  ], []);

  return (
    <div className="bg-dark-100 rounded-xl shadow-lg border border-primary-700 p-6">
      <h3 className="text-lg font-semibold text-white mb-4 text-center">
        {t('converter.selectFormat')}
      </h3>

      <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-9 gap-3">
        {formats.map((format) => (
          <button
            key={format.id}
            className={cn(
              "relative flex flex-col items-center justify-center p-3 rounded-lg border-2 transition-all duration-300 hover:scale-105",
              selectedFormat === format.id
                ? "border-accent-600 bg-accent-600/20 shadow-lg"
                : "border-primary-700 bg-dark-50 hover:border-accent-600 hover:bg-accent-600/10 hover:shadow-md"
            )}
            onClick={() => onFormatChange(format.id)}
          >
            <span className="text-xl mb-1">{format.icon}</span>
            <span className="text-xs font-semibold text-white">
              {t(`formats.${format.id}`)}
            </span>

            {selectedFormat === format.id && (
              <div className="absolute -top-1 -right-1">
                <CheckCircle className="w-4 h-4 text-accent-500 fill-dark-100 shadow-lg rounded-full" />
              </div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
}