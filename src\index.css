@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-white text-primary-900 antialiased;
  }

  ::selection {
    @apply bg-accent-200 text-accent-900;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-xl px-6 py-3 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm;
  }

  .btn-primary {
    @apply bg-accent-500 text-white hover:bg-accent-600 hover:shadow-md;
  }

  .btn-secondary {
    @apply bg-primary-100 text-primary-700 hover:bg-primary-200 border border-primary-200;
  }

  .btn-outline {
    @apply border border-primary-200 bg-white hover:bg-primary-50 text-primary-700 hover:border-primary-300;
  }

  .container-custom {
    @apply px-6 mx-auto max-w-6xl sm:px-8 lg:px-12;
  }

  .drop-area {
    @apply border-2 border-dashed rounded-2xl p-12 transition-all duration-300 ease-in-out;
  }

  .drop-area-active {
    @apply border-accent-400 bg-accent-50;
  }
}