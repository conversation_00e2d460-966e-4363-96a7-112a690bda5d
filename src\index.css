@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-dark-200 text-white antialiased;
  }

  ::selection {
    @apply bg-accent-600 text-white;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none shadow-sm;
  }

  .btn-primary {
    @apply bg-accent-600 text-white hover:bg-accent-700 hover:shadow-lg border border-accent-600;
  }

  .btn-secondary {
    @apply bg-dark-50 text-white hover:bg-primary-800 border border-primary-700;
  }

  .btn-outline {
    @apply border border-primary-700 bg-dark-100 hover:bg-dark-50 text-white hover:border-accent-600;
  }

  .container-custom {
    @apply px-4 mx-auto max-w-6xl sm:px-6 lg:px-8;
  }

  .drop-area {
    @apply border-2 border-dashed rounded-xl p-8 transition-all duration-300 ease-in-out bg-dark-100 border-primary-700;
  }

  .drop-area-active {
    @apply border-accent-500 bg-dark-50;
  }
}