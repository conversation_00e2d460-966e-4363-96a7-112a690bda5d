import { CheckCircle, DownloadCloud, Zap, Lock } from 'lucide-react';

export default function Features() {
  const features = [
    {
      icon: <Zap className="w-6 h-6 text-primary-500" />,
      title: 'Convers<PERSON> Rápida',
      description: 'Converta arquivos em segundos, sem necessidade de instalação de software.'
    },
    {
      icon: <CheckCircle className="w-6 h-6 text-primary-500" />,
      title: 'Alta Qualidade',
      description: 'Mantém a qualidade original das suas imagens durante o processo de conversão.'
    },
    {
      icon: <DownloadCloud className="w-6 h-6 text-primary-500" />,
      title: 'Fácil de Usar',
      description: 'Interface intuitiva que facilita o processo de conversão para qualquer usuário.'
    },
    {
      icon: <Lock className="w-6 h-6 text-primary-500" />,
      title: '100% Seguro',
      description: 'Seus arquivos são processados no seu navegador. Não armazenamos seus dados.'
    },
  ];

  return (
    <div id="features" className="bg-gray-50 py-16">
      <div className="container-custom">
        <div className="max-w-3xl mx-auto text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Por que escolher ICOnverter?
          </h2>
          <p className="text-lg text-gray-600">
            Nossa ferramenta de conversão de imagens oferece vantagens exclusivas
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow duration-200"
            >
              <div className="rounded-full bg-primary-50 p-3 inline-flex mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}