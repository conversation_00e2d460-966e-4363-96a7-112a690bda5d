import { useEffect } from 'react';
import Converter from './components/Converter';
import './i18n';

function App() {
  useEffect(() => {
    document.title = 'ICOnverter - Conversor de Imagens Online';
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-white flex items-center justify-center p-6">
      <main className="w-full max-w-4xl">
        <Converter />
      </main>
    </div>
  );
}

export default App;