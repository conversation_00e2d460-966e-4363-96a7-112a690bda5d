import { useEffect } from 'react';
import Converter from './components/Converter';
import './i18n';

function App() {
  useEffect(() => {
    document.title = 'ICOnverter - Conversor de Imagens Online';
  }, []);

  return (
    <div className="min-h-screen bg-dark-200 flex items-start justify-center p-4 pt-6">
      <main className="w-full max-w-5xl">
        <Converter />
      </main>
    </div>
  );
}

export default App;