import { ArrowDownCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ConvertedFilePreviewProps {
  originalFile: File;
  convertedFormat: string;
  onDownload: () => void;
}

export default function ConvertedFilePreview({ 
  originalFile, 
  convertedFormat,
  onDownload 
}: ConvertedFilePreviewProps) {
  const { t } = useTranslation();
  
  // In a real app, we'd show the actual converted file preview
  // Here we're simulating with the original file's preview
  const fileName = originalFile.name.split('.')[0] + '.' + convertedFormat;
  
  return (
    <div className="relative group rounded-lg overflow-hidden shadow-sm border border-gray-200 bg-white">
      <div className="h-40 flex items-center justify-center bg-gray-50 p-2">
        <div className="text-4xl">
          {convertedFormat === 'ico' && '🖼️'}
          {convertedFormat === 'png' && '🎨'}
          {convertedFormat === 'jpg' && '📷'}
          {convertedFormat === 'gif' && '🎞️'}
          {convertedFormat === 'webp' && '🌐'}
          {convertedFormat === 'svg' && '✨'}
        </div>
      </div>
      <div className="p-3">
        <p className="text-sm font-medium text-gray-700 truncate" title={fileName}>
          {fileName}
        </p>
        <div className="flex justify-between items-center mt-2">
          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
            Converted
          </span>
          <button 
            onClick={onDownload}
            className="text-primary-500 hover:text-primary-600 transition-colors"
            aria-label="Download file"
          >
            <ArrowDownCircle className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}