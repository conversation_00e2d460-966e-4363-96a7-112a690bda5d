import { ArrowDownCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface ConvertedFilePreviewProps {
  originalFile: File;
  convertedFormat: string;
  onDownload: () => void;
}

export default function ConvertedFilePreview({ 
  originalFile, 
  convertedFormat,
  onDownload 
}: ConvertedFilePreviewProps) {
  const { t } = useTranslation();
  
  // In a real app, we'd show the actual converted file preview
  // Here we're simulating with the original file's preview
  const fileName = originalFile.name.split('.')[0] + '.' + convertedFormat;
  
  return (
    <div className="relative group rounded-lg overflow-hidden shadow-sm border border-primary-700 bg-dark-50">
      <div className="h-24 flex items-center justify-center bg-dark-100 p-2">
        <div className="text-2xl">
          {convertedFormat === 'ico' && '🖼️'}
          {convertedFormat === 'png' && '🎨'}
          {convertedFormat === 'jpg' && '📷'}
          {convertedFormat === 'gif' && '🎞️'}
          {convertedFormat === 'webp' && '🌐'}
          {convertedFormat === 'svg' && '✨'}
          {convertedFormat === 'bmp' && '🖌️'}
          {convertedFormat === 'tiff' && '📄'}
          {convertedFormat === 'pdf' && '📋'}
        </div>
      </div>
      <div className="p-2">
        <p className="text-xs font-medium text-white truncate" title={fileName}>
          {fileName}
        </p>
        <div className="flex justify-between items-center mt-1">
          <span className="text-xs bg-accent-600 text-white px-2 py-0.5 rounded-full">
            Convertido
          </span>
          <button
            onClick={onDownload}
            className="text-accent-500 hover:text-accent-400 transition-colors"
            aria-label="Download file"
          >
            <ArrowDownCircle className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}